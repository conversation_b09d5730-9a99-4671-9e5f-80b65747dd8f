package local.bestoon.ui.components

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.Button
import android.widget.CheckBox
import android.widget.LinearLayout
import local.bestoon.R
import local.bestoon.data.model.TagItem

/**
 * Custom tags multi-select dialog with consistent styling.
 */
class TagsMultiSelectDialog(
    context: Context,
    private val tags: List<TagItem>,
    private val selectedTagIds: List<Int>,
    private val onTagsSelected: (selectedIds: List<Int>, selectedTitles: List<String>) -> Unit
) : Dialog(context) {

    private lateinit var tagsContainer: LinearLayout
    private lateinit var btnCancel: Button
    private lateinit var btnOk: Button
    
    private val checkBoxes = mutableListOf<CheckBox>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_tags_multi_select, null)
        setContentView(view)

        // Initialize views
        tagsContainer = view.findViewById(R.id.ll_tags_container)
        btnCancel = view.findViewById(R.id.btn_cancel)
        btnOk = view.findViewById(R.id.btn_ok)

        setupTagsList()
        setupButtons()
        
        // Set dialog properties
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupTagsList() {
        tagsContainer.removeAllViews()
        checkBoxes.clear()

        tags.forEach { tag ->
            val checkBox = LayoutInflater.from(context)
                .inflate(R.layout.item_tag_checkbox, tagsContainer, false) as CheckBox
            
            checkBox.text = tag.title
            checkBox.isChecked = tag.id in selectedTagIds
            checkBox.tag = tag.id
            
            checkBoxes.add(checkBox)
            tagsContainer.addView(checkBox)
        }
    }

    private fun setupButtons() {
        btnCancel.setOnClickListener {
            dismiss()
        }

        btnOk.setOnClickListener {
            val selectedIds = mutableListOf<Int>()
            val selectedTitles = mutableListOf<String>()
            
            checkBoxes.forEach { checkBox ->
                if (checkBox.isChecked) {
                    val tagId = checkBox.tag as Int
                    val tag = tags.find { it.id == tagId }
                    if (tag != null) {
                        selectedIds.add(tag.id)
                        selectedTitles.add(tag.title)
                    }
                }
            }
            
            onTagsSelected(selectedIds, selectedTitles)
            dismiss()
        }
    }
}
